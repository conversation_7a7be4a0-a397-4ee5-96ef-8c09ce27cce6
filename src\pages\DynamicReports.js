import React, { useState, useEffect, useRef, useMemo, useContext } from "react";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import {
  CloseIcon,
  InfoIcon,
  MenusIcon,
  MailBoxIcon,
  SearchhIcon,
  ChartIcon,
  TableChartIcon,
} from "../icons";
import { useNavigate, useLocation } from "react-router-dom";
import Button from "../components/Button/OutlinedButton";
import { CssTooltip } from "../components/StyledComponent";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";
import "react-toastify/dist/ReactToastify.css";
import ReportFilter from "../components/CollapsibleFilter/ReportFilter";
import { MetaDataProvider } from "../context/MetaDataContext";
import ReportCalendar from "../components/DatePicker/ReportCalendar";
import { useMutation, useQuery } from "react-query";
import { getId, previewPanel, downloadPanel } from "../services/panels-api";
import { CircularProgress } from "@mui/material";
import { SelectedFiltersDisplay } from "../common/selectedFiltersDisplay";
import bgImage from "../assets/img/Records.png";
import { DataContext } from "../context/DataContext";
import ExportPopup from "../popups/exportpopup";
import SendMail from "../popups/SendMail";
import theme from "../tailwind-theme";
import { ToastContainer } from "react-toastify";
import ErrorDialog from "../popups/ErrorDialog";
import SuccessDialog from "../popups/SuccessDialog";
import AggregationTable from "../components/table/AggregationTable";
import { AuthContext } from "../context/AuthContext";
import { DownloadContext } from "../context/DownloadContext";
import ColumnSelector from "../components/ColumnSelector/ColumnSelector";

// Utility imports
import {
  getFormattedDateRange,
  generateReportFilename,
  mapDurationToInterval,
} from "../utils/dateRangeUtils";
import {
  getExportPermissions,
  createExportTooltipContent,
  getExportTypeMapping,
} from "../utils/exportUtils";
import { generatePDFFromElement } from "../utils/pdfUtils";
import {
  buildRequestData,
  generateTableColumns,
  getTimeRangeOptions,
  createApiCallKey,
  checkDownloadPermission,
} from "../utils/reportUtils";
import {
  useClickOutside,
  useSearch,
  usePagination,
} from "../utils/reportHooks";
import { getSelectedColumn } from "../services/staticreport.service";
import { filterLabelMap, REPORT_INFO_TOOLTIP } from "../common/constants";
import InfoModal from "../components/modals/InfoModal";

import DynamicReportGraphView from "./DynamicReportGraphView";
import VisualizationRenderer from "../components/reports/VisualizationRenderer";
import { exportDataToCSV } from "../utils/csvExportUtils";

// Helper function to check if filter value is valid
const isValidFilterValue = (value) =>
  value !== null &&
  value !== undefined &&
  value !== "" &&
  value !== false &&
  (!Array.isArray(value) || value.length > 0);

function DynamicReports({ onClose }) {
  const location = useLocation();
  const { value: data, viewBy, activeTab, subtype, id } = location.state || {};
  const [filters, setFilters] = useState({});
  const [labelData, setLabelData] = useState([]);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRange, setSelectedRange] = useState("Today");
  const [filterDialog, setFilterDialog] = useState(false);
  const [panelById, setPanelById] = useState("");
  const [responseData, setResponseData] = useState([]);
  const [errorMessage, setErrorMessage] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [searchStr, setSearchStr] = useState("");
  const [sendMailDialog, setSendMailDialog] = useState(false);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [message, setMessage] = useState("");
  const [extensionType, setExtensionType] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [totalCount, setTotalCount] = useState("");
  const [selectedFilter, setSelectedFilter] = useState({
    startDate: null,
    endDate: null,
    selectedRange: null,
    duration: null,
  });
  const [appliedCalendar, setAppliedCalendar] = useState(false);
  const [payloadData, setPayloadData] = useState({});
  const [lastInterval, setLastInterval] = useState(undefined); // <-- Add this line
  const lastCallParamsRef = useRef(null);
  const previewByIdRef = useRef(null);
  const { resultPerPage } = useContext(DataContext);
  const { roles, user, configApiData } = useContext(AuthContext);
  const [doFetch, setDoFetch] = useState(false);
  const { setIsDownloading } = useContext(DownloadContext);
  const [download, setDownload] = useState(false);
  const [isDowloadLoading, setIsDownloadLoading] = useState(false);
  const [filterFlag, setFilterFlag] = useState(false);
  const [errMsg, setErrMsg] = useState("");
  const [allTableColumns, setAllTableColumns] = useState([]);
  const [visibleColumns, setVisibleColumns] = useState({});
  const [derivedFields, setDerivedFields] = useState([]);
  const [selectedColumn, setSelectedColumn] = useState({});
  const [activeView, setActiveView] = useState("table");
  const [graphFilters, setgraphFilters] = useState({});
  const [showGraph, setShowGraph] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [graphPreference, setGraphPreference] = useState({});
  const [lastSetGraphFilters, setLastSetGraphFilters] = useState({});

  // Helper to always keep first table field enabled only if 'Date' is not present
  const getEnforcedVisibleColumns = (columnsObj, allFields, derivedFields) => {
    if (allFields.includes("Date")) {
      return columnsObj;
    }
    const tableFields = allFields.filter(
      (label) => !derivedFields || !derivedFields.includes(label)
    );
    const firstTableField = tableFields.length > 0 ? tableFields[0] : null;
    if (firstTableField) {
      return { ...columnsObj, [firstTableField]: true };
    }
    return columnsObj;
  };

  // Use click outside hook for menu dropdown
  const menuRef = useClickOutside(showMenuDropdown, () =>
    setShowMenuDropdown(false)
  );

  // Check download permission using utility function
  const permission = checkDownloadPermission(
    user,
    roles,
    data,
    location.state?.creater
  );

  const isTableReport = panelById?.visualizationType === "Table Report";
  const isTableView = activeView === "table";
  const isGraphView = activeView === "graph";

  const tooltipContent = createExportTooltipContent(configApiData);

  // Common tooltip content
  const tooltipTitle =
    isTableReport && isGraphView
      ? "If the graph contains a large dataset, please apply filters for better visibility and accurate downloads."
      : isTableReport && isTableView
      ? tooltipContent
      : "";

  // Common click handler
  const handleDownloadClick = () => {
    if (permission === 0) {
      setMessage("Download permission not allowed");
      setErrorDialog(true);
      return;
    }

    if (totalCount === 0) {
      setMessage("No data to download");
      setErrorDialog(true);
      return;
    }

    if (isTableReport && isTableView) {
      if (errMsg) {
        setMessage(
          errMsg
            ? "You don't have permission to download this report."
            : "Error downloading report"
        );
        setErrorDialog(true);
      } else {
        setShowExportConfirmation(true);
      }
    } else {
      setShowExportConfirmation(true);
    }
  };

  // Create separate refs for each chart type
  const pieChartRef = useRef(null);

  // Use search hook
  const { handleChange } = useSearch((value) => {
    setSearchStr(value);
    setCurrentPage(1);
    setDoFetch(true);
  });
  // Utility to export CSV using exportDataToCSV
  // import exportDataToCSV at the top

  // Export report function
  const exportReport = (type) => {
    setExtensionType(type);
    if (panelById?.visualizationType === "Table Report") {
      if (activeView === "graph") {
        if (type === "CSV") {
          // Use exportDataToCSV utility
          const graphData =
            panelById?.visualizationType === "multiAxis"
              ? responseData
              : responseData?.data;
          if (!graphData || !graphData.length) {
            setMessage("No data available for CSV download");
            setErrorDialog(true);
            return;
          }
          const fileName = `${panelById?.name || "graph_data_report"}_${
            selectedFilter?.startDate || ""
          }_${selectedFilter?.endDate || ""}`;
          exportDataToCSV({
            data: graphData,
            filename: fileName,
            options: {
              title: `Reports for ${selectedFilter.startDate} to ${selectedFilter.endDate}`,
            },
            onStart: () => {
              setIsDownloading(true);
              setIsDownloadLoading(true);
            },
            onSuccess: () => {
              setIsDownloading(false);
              setIsDownloadLoading(false);
            },
            onError: (errorMessage) => {
              setMessage(
                errorMessage || "Error generating CSV. Please try again."
              );
              setErrorDialog(true);
              setIsDownloading(false);
              setIsDownloadLoading(false);
            },
          });
        } else if (type === "PDF") {
          handleDownloadPDF();
        }
        return;
      }
      if (totalCount < configApiData.INITIATE_OFFLINE_DOWNLOAD) {
        setIsDownloading(true);
      } else {
        setDownload(true);
      }
      handleTableReportDownload(type);
    } else {
      handleDownloadPDF();
    }
  };

  // Use utility function for export permissions
  const exportPermissions = getExportPermissions(totalCount, configApiData);

  // Use utility function for tooltip content

  const handleTableReportDownload = (type = "CSV") => {
    if (!panelById) return;
    setIsDownloadLoading(true);

    // Use utility function for type mapping
    const { apiType } = getExportTypeMapping(type);
    const { limit, page, ...filteredPayload } = payloadData;

    // Use utility function for filename generation
    const filename = generateReportFilename(
      panelById.name,
      selectedFilter.startDate,
      selectedFilter.endDate
    );
    const removedExtension = filename.split(".")[0];
    let reqData = {
      ...filteredPayload,
      download: 1,
      type: apiType,
      totalCount: totalCount,
      fileName: removedExtension,
    };
    // Call download API
    downloadPanel({ reqData })
      .then((response) => {
        setIsDownloadLoading(false);
        if (totalCount > configApiData.INITIATE_OFFLINE_DOWNLOAD) {
          setSuccessDialog(true);
          setMessage(
            "Your offline download is initiated, Please view the status of your download in Offline Downloads menu"
          );
        } else {
          const url = URL.createObjectURL(response.data);
          const link = document.createElement("a");
          link.href = url;
          const fileName = filename + ".zip";
          link.download = fileName;
          link.click();
          URL.revokeObjectURL(url);
        }
      })
      .catch((error) => {
        setMessage("Error downloading report. Please try again.");
        setErrorDialog(true);
        setIsDownloading(false);
        setDownload(false);
      })
      .finally(() => {
        setIsDownloading(false);
        setDownload(false);
        setIsDownloadLoading(false);
      });
  };
  const handleDownloadPDF = () => {
    const visualizationType = panelById?.visualizationType;

    if (visualizationType === "Table Report" && activeView === "table") {
      handleTableReportDownload();
      return;
    }

    let targetElement = pieChartRef.current;

    if (targetElement) {
      setIsDownloading(true);
      setIsDownloadLoading(true);

      // Get formatted date range using utility function
      const { formattedStart, formattedEnd } = getFormattedDateRange(
        panelById.timePeriod
      );

      // Generate filename using utility function
      const filename = generateReportFilename(
        panelById.name,
        formattedStart,
        formattedEnd
      );

      // Use utility function for PDF generation
      generatePDFFromElement(
        targetElement,
        filename,
        panelById.name || "Report",
        () => {
          setIsDownloading(false);
          setIsDownloadLoading(false);
        },
        (error) => {
          setMessage("Error generating PDF. Please try again.");
          setErrorDialog(true);
          setIsDownloading(false);
          setIsDownloadLoading(false);
        }
      );
    }
  };
  const navigate = useNavigate();
  dayjs.extend(customParseFormat);

  const getUrl = new URL(window.location.href);
  const timeZone = getUrl.search.slice(1);
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];

  // Use pagination hook
  const { handlePageChange, handleLimitChange } = usePagination(
    (page) => {
      setCurrentPage(page);
      setDoFetch(true);
    },
    (page, limit) => {
      setCurrentPage(page);
      setLimitPerPage(limit);
      setDoFetch(true);
    }
  );

  const filteredKeys = Object.keys(filters || {}).filter(
    (key) => key !== "durationTime"
  );

  const badgeCount = filteredKeys.length;

  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);

  const { isLoading: panelLoading } = useQuery(["getPanelById", id], getId, {
    enabled: !!id,
    onSuccess: ({ data }) => {
      setPanelById(data);
      if (data?.visualizationType !== "Table Report") {
        setDoFetch(true);
        setActiveView("graph");
      }
      setSelectedRange(data?.timePeriod);
    },
    refetchOnWindowFocus: false,
  });

  const previewById = (data, reportFilters = {}, isDownload = false) => {
    console.log("validation", graphFilters);
    if (
      !doFetch ||
      (panelById.visualizationType === "Table Report" &&
        activeView === "graph" &&
        graphFilters &&
        Object.keys(graphFilters).length <= 0)
    ) {
      return;
    }
    if (
      activeView === "graph" &&
      panelById.visualizationType === "Table Report"
    ) {
      refetch();
    }

    setDoFetch(false);

    const selectedRange = data.timePeriod;

    const { formattedStart, formattedEnd } = getFormattedDateRange(
      selectedRange,
      selectedFilter
    );

    // Update selectedFilter if we got new dates
    if (formattedStart && formattedEnd && !selectedFilter?.startDate) {
      setSelectedFilter({
        startDate: formattedStart,
        endDate: formattedEnd,
        selectedRange: selectedRange,
        duration: null,
      });
    }

    let intervalData = undefined;
    if (data.interval && !appliedCalendar) {
      intervalData = data.interval;
      setLastInterval(data.interval); // Save the interval when calendar is applied
    } else if (selectedFilter.duration) {
      intervalData = mapDurationToInterval(selectedFilter.duration);
      setLastInterval(intervalData); // Save the interval if duration is used
    } else if (lastInterval) {
      intervalData = lastInterval; // Use last interval if available
    }

    // Use utility function to build request data
    const reqData = buildRequestData({
      data,
      formattedStart,
      formattedEnd,
      timeZone,
      reportFilters,
      searchStr,
      intervalData,
      isDownload,
      limitPerPage,
      currentPage,
      activeView,
      configApiData,
      graphFilters,
      id,
    });
    if (
      selectedColumn &&
      (selectedColumn.selectedDerivedFields?.length > 0 ||
        selectedColumn.selectedTableFields?.length > 0)
    ) {
      reqData.dataColumns.tableFields =
        selectedColumn.selectedTableFields || [];
      reqData.dataColumns.derivedFields =
        selectedColumn.selectedDerivedFields || [];
    }
    if (
      activeView === "graph" &&
      panelById.visualizationType === "Table Report"
    ) {
      reqData.isGraph = true;
    } else {
      reqData.isGraph = false;
    }
    setPayloadData(reqData);

    previewPanelAPIData(
      { reqData },
      {
        onSuccess: ({ data }) => {
          setErrorMessage(false);
          setResponseData(data);
          setTotalCount(data.totalCount);
          if (
            data.totalCount > configApiData?.GRAPH_PAGE_LIMIT &&
            activeView === "graph" &&
            panelById.visualizationType === "Table Report"
          ) {
            setShowAlertConfirmation(true);
            setMessage(
              "Reframe from using huge data to be displayed in the graph.Selecting these many will result in a cluttered and unreadable graph. Please select optimal data."
            );
            setShowGraph(false);
          } else {
            setShowGraph(true);
          }
          setFilterFlag(data.filterFlag);
          setErrMsg("");
        },
        onError: (error) => {
          setErrMsg(error?.response?.data?.message);
          setErrorMessage(true);
          setResponseData([]);
          setShowGraph(false);
        },
      }
    );
  };

  previewByIdRef.current = previewById;

  useEffect(() => {
    if (selectedRange && doFetch) {
      // Use utility function to get formatted date range
      const { formattedStart, formattedEnd } =
        getFormattedDateRange(selectedRange);

      if (selectedRange.includes("to")) {
        setSelectedRange("Calendar");
      }

      // Update selectedFilter with the calculated dates
      if (formattedStart && formattedEnd) {
        setSelectedFilter({
          startDate: formattedStart,
          endDate: formattedEnd,
          selectedRange: selectedRange,
          duration: null,
        });
      }
    }
  }, [selectedRange, doFetch]);

  useEffect(() => {
    if (panelById && previewByIdRef.current) {
      const currentFilters =
        Object.keys(filters ?? {}).length > 0 ? filters : {};

      // Create a unique key for this call to prevent duplicates using utility function
      const callKey = createApiCallKey({
        panelId: panelById.id,
        filters: currentFilters,
        currentPage,
        limitPerPage,
        searchStr,
        selectedFilter: selectedFilter,
      });

      lastCallParamsRef.current = callKey;

      // Update panelById with new time period if selectedFilter has changed
      const updatedPanelById = {
        ...panelById,
        timePeriod: selectedFilter?.selectedRange || panelById.timePeriod,
      };

      previewByIdRef.current(updatedPanelById, currentFilters);
    }
  }, [
    panelById,
    filters,
    currentPage,
    limitPerPage,
    searchStr,
    selectedFilter,
  ]);

  const { data: columnConfigData, refetch } = useQuery(
    ["reportList", "dynamic", data, id],
    getSelectedColumn,
    {
      enabled: !!id && panelById?.visualizationType === "Table Report",
      onSuccess: ({ data }) => {
        const { dataColumns, selectedColumns = [] } = data || {};

        if (!dataColumns) return;

        const { tableFields = [], derivedFields = [] } = dataColumns;
        // Split selectedColumns into tableFields and derivedFields
        const selectedTableFields = selectedColumns.filter((col) =>
          tableFields.includes(col)
        );
        const selectedDerivedFields = selectedColumns.filter((col) =>
          derivedFields.includes(col)
        );
        setSelectedColumn({
          selectedTableFields,
          selectedDerivedFields,
        });
        setGraphPreference(data?.graphPreference);
        const combinedFields = [...tableFields, ...derivedFields];

        setDerivedFields(derivedFields);
        setAllTableColumns(combinedFields);
        if (Object.keys(visibleColumns).length > 0) return;
        const initialVisibility = combinedFields.reduce((acc, field) => {
          acc[field] =
            selectedColumns.length > 0 ? selectedColumns.includes(field) : true;
          return acc;
        }, {});
        setVisibleColumns(
          getEnforcedVisibleColumns(
            initialVisibility,
            combinedFields,
            derivedFields
          )
        );
        setDoFetch(true);
      },
      refetchOnWindowFocus: false,
    }
  );

  // Use utility function for generating table columns
  const columns = useMemo(() => {
    const allColumns = generateTableColumns(
      responseData,
      panelById?.visualizationType
    );

    // Filter columns based on visibility for table reports
    if (
      panelById?.visualizationType === "Table Report" &&
      Object.keys(visibleColumns).length > 0
    ) {
      return allColumns.filter(
        (column) => visibleColumns[column.accessor] !== false
      );
    }

    return allColumns;
  }, [responseData, panelById?.visualizationType, visibleColumns]);

  const handleTabView = (view) => {
    setActiveView(view);
    if (view === "table") {
      setCurrentPage(1);
      setDoFetch(true);
      // setSelectedColumn([]);
      setgraphFilters({});
      setShowGraph(false);
      setFilterFlag(false);
      setFilters({});
      setLabelData([]);
    } else {
      setgraphFilters(
        lastSetGraphFilters?.graphFilters
          ? lastSetGraphFilters?.graphFilters
          : graphPreference?.graphFilters
      );
      setFilters(
        lastSetGraphFilters?.filters
          ? lastSetGraphFilters?.filters
          : graphPreference?.filters
      );
      setResponseData([]);
      setTotalCount(0);
      setDoFetch(true);
    }
  };

  useEffect(() => {
    if (graphPreference?.filters && activeView === "graph") {
      // Process roaming status and generate applied labels
      const normalizedValues = graphPreference.filters;
      const appliedLabels = [];
      const statusMap = {
        only_roaming: "Only Roaming",
        only_direct: "Only Direct",
        both: "Roaming and Direct",
      };

      // Check if any roaming status is set to true in the filters
      const hasRoamingDirect = Object.keys(statusMap).some(
        (status) => normalizedValues[status] === true
      );

      const filteredValues = {};

      if (hasRoamingDirect) {
        Object.entries(statusMap).forEach(([key, label]) => {
          if (normalizedValues[key] === true) {
            appliedLabels.push(label);
            filteredValues[key] = true;
          }
        });
      }

      // Add other labels
      Object.keys(normalizedValues).forEach((key) => {
        if (
          key !== "roamingDirectStatus" &&
          !Object.keys(statusMap).includes(key) &&
          filterLabelMap[key] &&
          isValidFilterValue(normalizedValues[key])
        ) {
          appliedLabels.push(filterLabelMap[key]);
        }
      });

      // Set the applied labels
      setLabelData(appliedLabels);
    }
  }, [graphPreference, activeView]);

  return (
    <>
      {/* Sticky Header with Breadcrumb */}
      <div className="sticky top-0 z-10 w-full bg-bgPrimary flex items-start text-headingColor text-2xl font-bold leading-tight">
        <BreadcrumbNavigation
          linkTwo="Static Reports"
          onlinkTwoClick={() =>
            navigate("/app/reports", {
              state: { tab: activeTab, subType: subtype },
            })
          }
          title={data}
        />
      </div>

      {/* Main Content Container */}
      <div className="bg-white p-3">
        {/* Close Icon Section */}
        <div className="flex justify-end items-center">
          <CloseIcon
            onClick={() =>
              navigate("/app/reports", {
                state: { tab: activeTab, subType: subtype },
              })
            }
            className="w-2 h-2 cursor-pointer mb-1"
          />
        </div>

        {/* Search and Controls */}
        <div className="mx-3 flex flex-wrap items-center justify-between gap-y-3">
          {/* Search Input */}
          {panelById?.visualizationType === "Table Report" &&
          activeView === "table" ? (
            <div className="w-full md:w-[300px] relative">
              <input
                type="text"
                style={{
                  border: `1px solid ${theme.borderColor.outerBorder}`,
                  paddingLeft: "2.5rem",
                }}
                className="w-full text-tabColor bg-white rounded-md focus:outline-none text-sm h-10"
                placeholder="Search"
                value={searchStr}
                onChange={handleChange}
              />
              <div className="absolute top-3 left-3">
                <SearchhIcon className="w-4 h-4" />
              </div>
            </div>
          ) : (
            <div className="w-full md:w-[300px] relative"></div>
          )}

          {/* Calendar, Info Tooltip, Mail, Filter, Refresh, Download */}
          <div className="flex items-center space-x-8">
            {/* Column Selector - Only show for Table Reports */}
            {panelById?.visualizationType === "Table Report" &&
              activeView === "table" && (
                <ColumnSelector
                  allFields={allTableColumns}
                  appliedFields={visibleColumns}
                  derivedFields={derivedFields}
                  onApply={(newVisibleColumns) => {
                    setCurrentPage(1);
                    setVisibleColumns(
                      getEnforcedVisibleColumns(
                        newVisibleColumns,
                        allTableColumns,
                        derivedFields
                      )
                    );
                  }}
                  onTriggerPreview={(columnSelection) => {
                    setSelectedColumn(columnSelection);
                    setDoFetch(true);
                  }}
                  reportId={id}
                  reportName={data}
                  reportType={"dynamic"}
                />
              )}
            {/* Menu Icon with Dropdown - Now positioned first */}
            <div className="relative" ref={menuRef}>
              <div
                className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setShowMenuDropdown(!showMenuDropdown);
                  setOpenDialog(false);
                }}
              >
                <CssTooltip title={"Report Menu"} placement="top" arrow>
                  <MenusIcon className="w-5 h-5" />
                </CssTooltip>
              </div>

              {/* Dropdown Menu */}
              {showMenuDropdown && (
                <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200 p-3">
                  <div className="grid grid-cols-2 gap-2">
                    {/* Send Mail Option */}
                    <div
                      className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                      onClick={() => {
                        if (totalCount === 0) {
                          setMessage("No data available to send");
                          setErrorDialog(true);
                          return;
                        }
                        setSendMailDialog(true);
                        setShowMenuDropdown(false);
                      }}
                    >
                      <MailBoxIcon className="w-5 h-5" />
                      <span className="text-sm text-gray-700 ml-2">
                        Send Email
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
            {/* Info Tooltip */}
            <CssTooltip
              title={
                <div className="text-xs p-1">
                  {REPORT_INFO_TOOLTIP.map((text, idx) => (
                    <p className="mb-1.5" key={idx}>
                      {text}
                    </p>
                  ))}
                </div>
              }
              placement="top"
              arrow
            >
              <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
            </CssTooltip>
            {/* Calendar Picker - Disabled for weekly/monthly */}
            <div
              className={`${
                filters?.duration === "weekly" ||
                filters?.duration === "monthly"
                  ? "pointer-events-none opacity-50"
                  : ""
              }`}
              onClick={() => {
                setShowMenuDropdown(false);
              }}
            >
              <ReportCalendar
                selectedFilter={selectedFilter}
                setSelectedFilter={setSelectedFilter}
                setSelectedRange={setSelectedRange}
                selectedRange={selectedRange}
                reportTimeRange={getTimeRangeOptions(viewBy)}
                viewBy={viewBy}
                subtype={subtype}
                openDialog={openDialog}
                setOpenDialog={setOpenDialog}
                data={data}
                isAdmin={false}
                onApply={() => setDoFetch(true)}
                setAppliedCalendar={setAppliedCalendar}
              />
            </div>
            {/* Download Button */}
            <CssTooltip
              title={tooltipTitle}
              placement="top"
              arrow
              PopperProps={{
                modifiers: [
                  {
                    name: "preventOverflow",
                    options: { altBoundary: true },
                  },
                ],
              }}
              enterNextDelay={100}
              enterDelay={100}
              leaveDelay={200}
              componentsProps={{
                popper: { sx: { opacity: 1 } },
              }}
            >
              <Button
                buttonClassName="text-xs w-32 text-white h-10 rounded-md"
                label="Download"
                onClick={handleDownloadClick}
                disabled={loadingData || isDowloadLoading || totalCount === 0}
                loading={isTableReport && isTableView ? download : undefined}
              />
            </CssTooltip>
          </div>
        </div>

        {loadingData && !panelLoading && (
          <div className="mt-3 bg-white p-5 w-[65vw]">
            <div className="my-5 flex justify-center items-center">
              <CircularProgress size={40} />
            </div>
          </div>
        )}

        {!loadingData && responseData && panelById && (
          <>
            <div className="font-semibold flex items-center justify-between mb-3 m-5">
              <div>
                {"Report from "} {selectedFilter?.startDate || ""}
                {" to"} {selectedFilter?.endDate || ""}
              </div>
              {totalCount > 0 &&
                panelById?.filters.length > 0 &&
                Object.keys(filters).length === 0 && (
                  <div className="flex font-hebrew text-sm whitespace-nowrap">
                    <div className="flex">
                      Selected Filters:
                      <CssTooltip
                        title={
                          <SelectedFiltersDisplay
                            conditions={panelById?.filters}
                          />
                        }
                        placement="bottom"
                        arrow
                      >
                        <InfoIcon className="ml-2 w-4 mt-1 h-3.5" />
                      </CssTooltip>
                    </div>
                  </div>
                )}
            </div>
            {responseData.aggregateResponse && (
              <div className="mt-3 m-4">
                <AggregationTable
                  aggregateResponse={responseData.aggregateResponse}
                />
              </div>
            )}{" "}
            <div className="my-4 flex items-center border-b border-gray-200 justify-between">
              <div className="flex items-center">
                {["table", "graph"].map((view) =>
                  view === "table" &&
                  panelById.visualizationType !== "Table Report" ? null : (
                    <button
                      key={view}
                      className={`whitespace-nowrap py-1 px-4 border-b-2 font-medium text-sm ${
                        activeView === view
                          ? "border-[#DC3833] text-[#DC3833]"
                          : "border-transparent text-gray-500"
                      }`}
                      onClick={() => handleTabView(view)}
                    >
                      {view === "table" ? "Table View" : "Graph View"}
                    </button>
                  )
                )}
              </div>
              {/* Filters Button */}
              <div className="relative">
                <CssTooltip
                  title={
                    labelData.length > 0 ? (
                      <div className="text-xs p-1">
                        {labelData.map((label, idx) => (
                          <div key={idx} className="mb-1">
                            {label}
                          </div>
                        ))}
                      </div>
                    ) : (
                      "No filters applied"
                    )
                  }
                  placement="top"
                  arrow
                >
                  <button
                    className="whitespace-nowrap rounded-md border border-gray-300 px-4 py-2 font-medium text-sm text-gray-600 hover:border-[#DC3833] hover:text-[#DC3833] relative"
                    onClick={() => {
                      setFilterDialog(true);
                    }}
                  >
                    {activeView === "table" ? (
                      <span className="flex items-center gap-2">
                        <TableChartIcon className="w-4 h-4" />
                        Table Filter
                      </span>
                    ) : (
                      <span className="flex items-center gap-2">
                        <ChartIcon />
                        Chart Filter
                      </span>
                    )}
                    {badgeCount > 0 && (
                      <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-medium">
                        {badgeCount}
                      </div>
                    )}
                  </button>
                </CssTooltip>
              </div>
            </div>
            {(errorMessage || totalCount === 0) &&
            panelById.visualizationType === "Table Report" &&
            activeView === "table" ? (
              <div className="border border-outerBorder mb-5 mt-8">
                <div
                  className={`flex text-headingColor ${
                    errMsg ? "text-lg" : "text-2xl"
                  } justify-center font-bold m-5`}
                >
                  {errMsg ? errMsg : "  Oops! No records to display."}
                </div>
                <div className="flex justify-center my-10">
                  <img
                    src={bgImage}
                    className="h-[10%] w-[10%] object-cover"
                    alt="bg"
                  />
                </div>
              </div>
            ) : (
              <div
                className={`${
                  panelById.visualizationType === "Table Report"
                    ? "m-3"
                    : "md:flex gap-5 border border-outerBorder m-5 p-3"
                }`}
                ref={pieChartRef}
              >
                <VisualizationRenderer
                  panelById={panelById}
                  activeView={activeView}
                  responseData={responseData}
                  colors={colors}
                  columns={columns}
                  resultPerPage={resultPerPage}
                  limitPerPage={limitPerPage}
                  handleLimitChange={handleLimitChange}
                  currentPage={currentPage}
                  handlePageChange={handlePageChange}
                />
              </div>
            )}
            {activeView === "graph" &&
              panelById.visualizationType === "Table Report" &&
              showGraph && (
                <>
                  <div
                    ref={pieChartRef}
                    className="mt-8 text-center text-gray-500 border border-outerBorder py-5"
                  >
                    <DynamicReportGraphView
                      visualizationType={graphFilters?.visualizationType}
                      data={
                        graphFilters?.visualizationType === "multiAxis"
                          ? responseData?.data
                          : graphFilters?.visualizationType === "line"
                          ? // Transform data for LineChartGraph from {x_axis, y_axis} to flat array
                            responseData?.data?.x_axis?.map((xItem, index) => ({
                              ...xItem,
                              ...responseData?.data?.y_axis[index],
                            })) || []
                          : responseData?.data
                      }
                      config={graphFilters}
                      totalCount={totalCount}
                      configApiData={configApiData}
                      onSetupClick={() => {
                        setFilterDialog(true);
                      }}
                      showGraph={showGraph}
                    />
                  </div>
                </>
              )}
          </>
        )}
      </div>

      {/* Export Report Modal */}
      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        onConfirm={(type) => {
          exportReport(type);
          setShowExportConfirmation(false);
        }}
        title={"Export Report"}
        identity={"Reports"}
        exportPermissions={
          panelById?.visualizationType === "Table Report"
            ? activeView === "graph"
              ? { csv: true, pdf: true }
              : exportPermissions
            : { csv: true, excel: true, pdf: true }
        }
      />

      {/* Send Mail Dialog */}
      <SendMail
        openGroupDialog={sendMailDialog}
        closeGroupDialog={() => {
          setSendMailDialog(false);
        }}
        selectedFilter={selectedFilter}
        searchStr={searchStr}
        type={extensionType}
        reportName={data}
        timeZone={timeZone}
        payloadData={payloadData}
        dynamicReports={true}
        reportFilters={filters}
        columnConfigData={columnConfigData?.data}
        graphFilters={graphFilters}
        graphValidation={
          panelById.visualizationType === "Table Report" ? true : false
        }
      />

      {/* Toast Notifications */}
      <ToastContainer position="top-center" autoClose={3000} />

      {/* Error and Success Dialogs */}
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
        graph={activeView === "graph" ? true : false}
        openGraphFilter={() => {
          setFilterDialog(true);
        }}
        applyFilter={() => {
          setShowGraph(true);
        }}
      />
      {/* Conditional Filter Dialog */}
      {filterDialog && (
        <MetaDataProvider>
          <ReportFilter
            openFilterDialog={filterDialog}
            activeView={activeView}
            closeFilterDialog={() => {
              setFilterDialog(false);
            }}
            reportNameData={data}
            reportName={"Dynamic Report"}
            setFilters={setFilters}
            setgraphFilters={setgraphFilters}
            filterData={{ filters, graphFilters }}
            setLabelData={setLabelData}
            setCurrentPage={setCurrentPage}
            fieldData={columnConfigData}
            configApiData={configApiData}
            totalCount={totalCount}
            panelVisualizationType={panelById.visualizationType}
            setDoFetch={setDoFetch}
            filterFlag={filterFlag}
            graphValidation={
              panelById.visualizationType === "Table Report" ? true : false
            }
            graphPreference={graphPreference}
            reportId={id}
            setLastSetGraphFilters={setLastSetGraphFilters}
          />
        </MetaDataProvider>
      )}
    </>
  );
}

export default DynamicReports;
